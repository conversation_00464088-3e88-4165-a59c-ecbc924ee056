// Modal Component

// Create modal element when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create modal element if it doesn't exist
  if (!document.querySelector('.modal-overlay')) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-dialog">
        <div class="modal-header">
          <i class="modal-icon bi bi-question-circle"></i>
          <h3 class="modal-title">Xác nhận</h3>
        </div>
        <div class="modal-body">
          <p class="modal-message">Bạn có chắc chắn muốn thực hiện hành động này?</p>
        </div>
        <div class="modal-actions">
          <button class="modal-btn modal-btn-secondary modal-cancel">Hủy</button>
          <button class="modal-btn modal-btn-primary modal-confirm">Có</button>
        </div>
      </div>
    `;
    document.body.appendChild(modal);

    // Add event listeners
    setupModalEventListeners();
  }
});

// Setup modal event listeners
function setupModalEventListeners() {
  const modal = document.querySelector('.modal-overlay');
  const cancelBtn = modal.querySelector('.modal-cancel');
  const confirmBtn = modal.querySelector('.modal-confirm');

  // Close modal when clicking overlay
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      hideModal();
    }
  });

  // Close modal when clicking cancel button
  cancelBtn.addEventListener('click', () => {
    hideModal();
  });

  // Handle confirm button click
  confirmBtn.addEventListener('click', () => {
    const callback = modal.confirmCallback;
    hideModal();
    if (callback && typeof callback === 'function') {
      callback();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.classList.contains('show')) {
      hideModal();
    }
  });
}

/**
 * Show confirmation modal
 * @param {Object} options - Modal options
 * @param {string} options.title - Modal title
 * @param {string} options.message - Modal message
 * @param {string} options.icon - Bootstrap icon class (without 'bi-' prefix)
 * @param {string} options.confirmText - Confirm button text
 * @param {string} options.cancelText - Cancel button text
 * @param {Function} options.onConfirm - Callback function when confirmed
 */
function showConfirmModal(options = {}) {
  const modal = document.querySelector('.modal-overlay');
  if (!modal) return;

  const {
    title = 'Xác nhận',
    message = 'Bạn có chắc chắn muốn thực hiện hành động này?',
    icon = 'question-circle',
    confirmText = 'Có',
    cancelText = 'Hủy',
    onConfirm = null
  } = options;

  // Update modal content
  const titleElement = modal.querySelector('.modal-title');
  const messageElement = modal.querySelector('.modal-message');
  const iconElement = modal.querySelector('.modal-icon');
  const confirmBtn = modal.querySelector('.modal-confirm');
  const cancelBtn = modal.querySelector('.modal-cancel');

  titleElement.textContent = title;
  messageElement.textContent = message;
  iconElement.className = `modal-icon bi bi-${icon}`;
  confirmBtn.textContent = confirmText;
  cancelBtn.textContent = cancelText;

  // Store callback function
  modal.confirmCallback = onConfirm;

  // Show modal
  modal.classList.add('show');
  
  // Focus on cancel button by default for safety
  setTimeout(() => {
    cancelBtn.focus();
  }, 100);
}

/**
 * Hide modal
 */
function hideModal() {
  const modal = document.querySelector('.modal-overlay');
  if (modal) {
    modal.classList.remove('show');
    // Clear callback
    modal.confirmCallback = null;
  }
}

/**
 * Show delete history confirmation modal
 * @param {Function} onConfirm - Callback function when confirmed
 */
function showDeleteHistoryConfirmation(onConfirm) {
  showConfirmModal({
    title: 'Xóa lịch sử',
    message: 'Bạn có chắc chắn muốn xóa lịch sử hay không?',
    icon: 'trash',
    confirmText: 'Có',
    cancelText: 'Không',
    onConfirm: onConfirm
  });
}
