/* Notification Component Styles */
.notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: var(--primary-color);
  color: var(--background-color);
  padding: 15px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  z-index: var(--z-index-notification);
  transform: translateY(100px);
  opacity: 0;
  transition: var(--transition);
  max-width: 300px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.notification i {
  font-size: 20px;
  margin-right: 10px;
}

.notification-message {
  font-size: 14px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 576px) {
  .notification {
    bottom: 20px;
    right: 20px;
    left: 20px;
    max-width: none;
  }
}
