/* Recently Played Page Styles */
.recently-played-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  color: var(--text-color);
}

.recently-played-header {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-in-out;
}

.recently-played-header h1 {
  font-size: 42px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.recently-played-header p {
  font-size: 16px;
  color: var(--subtitle-color);
}

.recently-played-content {
  width: 100%;
  animation: slideUp 0.8s ease-in-out;
}

.empty-recently-played {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
}

.empty-recently-played i {
  font-size: 80px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.empty-recently-played h2 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--text-color);
}

.empty-recently-played p {
  font-size: 16px;
  color: var(--subtitle-color);
  max-width: 500px;
  margin-bottom: 25px;
}

.empty-recently-played .action-btn {
  background-color: rgba(105, 105, 170, 0.7);
  border: none;
  color: #fff;
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.empty-recently-played .action-btn:hover {
  background-color: #36e2ec;
  transform: scale(1.05);
}

.empty-recently-played .action-btn i {
  font-size: 18px;
  margin-right: 8px;
}

.recently-played-songs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.recently-played-song-item {
  background-color: var(--hover-light);
  border-radius: var(--border-radius);
  padding: 15px;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  border: 1px solid transparent;
}

.recently-played-song-item:hover {
  background-color: var(--hover-color);
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-light);
  border-color: var(--border-color);
}

.recently-played-song-item .img_play {
  position: relative;
  margin-bottom: 10px;
}

.recently-played-song-item img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: var(--border-radius-small);
}

.recently-played-song-item .img_play i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: var(--primary-color);
  opacity: 0;
  transition: var(--transition);
  cursor: pointer;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
}

.recently-played-song-item:hover .img_play i {
  opacity: 1;
}

.recently-played-song-item h5 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 5px;
}

.recently-played-song-item .subtitle {
  font-size: 12px;
  color: var(--subtitle-color);
  margin-bottom: 8px;
}

.recently-played-song-item .play-time {
  font-size: 11px;
  color: #888;
  font-style: italic;
}

.recently-played-song-item .song-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recently-played-song-item:hover .song-actions {
  opacity: 1;
}

.recently-played-song-item .song-actions i {
  cursor: pointer;
  color: #fff;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
}

.recently-played-song-item .song-actions i:hover {
  color: #36e2ec;
  transform: scale(1.1);
}

.add-to-library {
  cursor: pointer;
  color: #fff;
  font-size: 18px;
  transition: all 0.3s ease;
}

.add-to-library:hover {
  color: #36e2ec;
  transform: scale(1.1);
}

.add-to-library.in-library {
  color: #36e2ec;
}

/* Clear history button */
.clear-history-btn {
  background-color: rgba(220, 53, 69, 0.7);
  border: none;
  color: #fff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
  display: flex;
  align-items: center;
}

.clear-history-btn:hover {
  background-color: rgba(220, 53, 69, 0.9);
  transform: scale(1.05);
}

.clear-history-btn i {
  margin-right: 5px;
}

.recently-played-header-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 30px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .recently-played-songs {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .recently-played-songs {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .recently-played-header-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .recently-played-header h1 {
    font-size: 32px;
  }

  .recently-played-songs {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}
header .song_side nav ul li:nth-child(3) {
  color: #fff;
}
