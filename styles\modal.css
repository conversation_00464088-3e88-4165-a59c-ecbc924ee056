/* Modal Component Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-index-modal);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  backdrop-filter: blur(5px);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background-color: var(--sidebar-color);
  border-radius: var(--border-radius-large);
  padding: 30px;
  max-width: 450px;
  width: 90%;
  box-shadow: var(--box-shadow);
  transform: scale(0.8) translateY(-50px);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.modal-overlay.show .modal-dialog {
  transform: scale(1) translateY(0);
}

.modal-header {
  text-align: center;
  margin-bottom: 25px;
}

.modal-header .modal-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 15px;
  display: block;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.modal-body {
  text-align: center;
  margin-bottom: 30px;
}

.modal-body p {
  font-size: 16px;
  color: var(--subtitle-color);
  line-height: 1.5;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.modal-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  min-width: 100px;
}

.modal-btn-primary {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.modal-btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(54, 226, 236, 0.3);
}

.modal-btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.modal-btn-secondary:hover {
  background-color: var(--secondary-hover);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(105, 105, 170, 0.3);
}

/* Responsive styles */
@media (max-width: 576px) {
  .modal-dialog {
    padding: 25px 20px;
    margin: 20px;
  }

  .modal-actions {
    flex-direction: column;
    gap: 10px;
  }

  .modal-btn {
    width: 100%;
  }

  .modal-header .modal-icon {
    font-size: 40px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body p {
    font-size: 15px;
  }
}
