// Notification Component

// Create notification element when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create notification element if it doesn't exist
  if (!document.querySelector('.notification')) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.innerHTML = `
      <i class="bi bi-check-circle-fill"></i>
      <div class="notification-message"></div>
    `;
    document.body.appendChild(notification);
  }
});

/**
 * Show notification with message
 * @param {string} message - Message to display
 * @param {number} duration - Duration in milliseconds
 */
function showNotification(message, duration = 3000) {
  const notification = document.querySelector('.notification');
  const messageElement = notification.querySelector('.notification-message');
  
  // Set message
  messageElement.textContent = message;
  
  // Show notification
  notification.classList.add('show');
  
  // Hide notification after duration
  setTimeout(() => {
    notification.classList.remove('show');
  }, duration);
}
