// Search functionality for music app

document.addEventListener("DOMContentLoaded", () => {
  // Load player state first
  if (typeof loadPlayerState === "function") {
    loadPlayerState();
  }

  // Initialize search functionality
  initializeSearch();
});

function initializeSearch() {
  // Get search elements
  const search_result = document.querySelector(".search_result");
  const search_input = document.querySelector(".search input");

  // If search elements don't exist, return
  if (!search_result || !search_input) return;

  // Clear existing search results
  search_result.innerHTML = "";

  // Populate search results with songs
  songs.forEach((element) => {
    const { id, songName, poster } = element;
    let card = document.createElement("a");
    card.classList.add("card");
    card.href = `music-detail.html?id=${id}`;
    card.innerHTML = `
    <img src="${poster}" alt="">
    <div class="content">
       ${songName}
    </div>
    <i class="bi bi-heart add-to-library search-heart" data-id="${id}" title="Add to Library"></i>
    `;
    search_result.appendChild(card);
  });

  // Initialize heart icons for search results
  initializeSearchHeartIcons();

  // Add search input event listener
  search_input.addEventListener("input", () => {
    let search_value = search_input.value.toLowerCase();

    // Show search results container when there's input
    if (search_value.length > 0) {
      search_result.style.display = "flex";
    } else {
      search_result.style.display = "none";
      return;
    }

    // Filter search results
    Array.from(search_result.getElementsByClassName("card")).forEach((card) => {
      let content = card
        .getElementsByClassName("content")[0]
        .innerText.toLowerCase();
      if (content.includes(search_value)) {
        card.style.display = "flex";
      } else {
        card.style.display = "none";
      }
    });
  });

  // Hide search results initially
  search_result.style.display = "none";

  // Add click event to search result cards
  Array.from(search_result.getElementsByClassName("card")).forEach((card) => {
    card.addEventListener("click", () => {
      // Extract the song ID from the href
      const songId = card.href.split("=")[1];

      // Set the current song index if index variable exists
      if (typeof index !== "undefined") {
        index = parseInt(songId);
      }
    });
  });
}

// Initialize heart icons for search results
function initializeSearchHeartIcons() {
  // Set initial state for search heart icons
  document.querySelectorAll(".search-heart").forEach((heartIcon) => {
    const songId = heartIcon.getAttribute("data-id");
    if (
      songId &&
      typeof isSongInLibrary === "function" &&
      isSongInLibrary(songId)
    ) {
      heartIcon.classList.remove("bi-heart");
      heartIcon.classList.add("bi-heart-fill", "in-library");
      heartIcon.title = "Remove from Library";
    }
  });

  // Add click event listeners to search heart icons
  document.querySelectorAll(".search-heart").forEach((heartIcon) => {
    heartIcon.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();

      const songId = e.target.getAttribute("data-id");
      if (!songId) return;

      // Check if library functions are available
      if (
        typeof isSongInLibrary !== "function" ||
        typeof addSongToLibrary !== "function" ||
        typeof removeSongFromLibrary !== "function"
      ) {
        console.warn("Library functions not available");
        return;
      }

      if (isSongInLibrary(songId)) {
        // Remove from library
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
        }
      } else {
        // Add to library
        if (addSongToLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
        }
      }
    });
  });
}
